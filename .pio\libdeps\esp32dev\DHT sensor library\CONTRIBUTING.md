# Contribution Guidlines

This library is the culmination of the expertise of many members of the open source community who have dedicated their time and hard work. The best way to ask for help or propose a new idea is to [create a new issue](https://github.com/adafruit/DHT-sensor-library/issues/new) while creating a Pull Request with your code changes allows you to share your own innovations with the rest of the community.

The following are some guidelines to observe when creating issues or PRs:

- Be friendly; it is important that we can all enjoy a safe space as we are all working on the same project and it is okay for people to have different ideas

- [Use code blocks](https://github.com/adam-p/markdown-here/wiki/Markdown-Cheatsheet#code); it helps us help you when we can read your code! On that note also refrain from pasting more than 30 lines of code in a post, instead [create a gist](https://gist.github.com/) if you need to share large snippets

- Use reasonable titles; refrain from using overly long or capitalized titles as they are usually annoying and do little to encourage others to help :smile:

- Be detailed; refrain from mentioning code problems without sharing your source code and always give information regarding your board and version of the library
