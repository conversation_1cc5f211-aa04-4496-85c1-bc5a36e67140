[env:nodemcu-32s]
platform = espressif32@^6.8.1
board = nodemcu-32s
framework = arduino
monitor_speed = 115200
monitor_filters = esp32_exception_decoder

; Library dependencies
lib_deps =
    adafruit/DHT sensor library@^1.4.6
    adafruit/Adafruit Unified Sensor@^1.1.14

; Upload settings
upload_speed = 115200
upload_port = AUTO

; Build settings
build_flags =
    -DCORE_DEBUG_LEVEL=3
    -DBOARD_HAS_PSRAM
    -DARDUINO_USB_CDC_ON_BOOT=0

; Monitor settings
monitor_rts = 0
monitor_dtr = 0
monitor_port = AUTO

; Board configuration
board_build.partitions = huge_app.csv
board_build.filesystem = littlefs
