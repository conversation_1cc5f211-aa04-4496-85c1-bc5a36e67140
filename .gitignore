# PlatformIO
.pio/
.vscode/.browse.c_cpp.db*
.vscode/ipch

# Compiled Object files
*.slo
*.lo
*.o
*.obj

# Precompiled Headers
*.gch
*.pch

# Compiled Dynamic libraries
*.so
*.dylib
*.dll

# Fortran module files
*.mod
*.smod

# Compiled Static libraries
*.lai
*.la
*.a
*.lib

# Executables
*.exe
*.out
*.app

# Debug files
*.dSYM/
*.su
*.idb
*.pdb

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
*.swp
*.swo
*~

# Temporary files
*.tmp
*.temp
*.log

# Build artifacts
build/
dist/
*.bin
*.elf
*.hex

# ESP32 specific
sdkconfig
sdkconfig.old

# IntelliSense helper files (local only)
include/sdkconfig.h
