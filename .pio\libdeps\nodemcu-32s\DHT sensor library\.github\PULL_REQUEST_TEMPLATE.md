Thank you for creating a pull request to contribute to Adafruit's GitHub code!
Before you open the request please review the following guidelines and tips to
help it be more easily integrated:

- **Describe the scope of your change--i.e. what the change does and what parts
  of the code were modified.**  This will help us understand any risks of integrating
  the code.

- **Describe any known limitations with your change.**  For example if the change
  doesn't apply to a supported platform of the library please mention it.

- **Please run any tests or examples that can exercise your modified code.**  We
  strive to not break users of the code and running tests/examples helps with this
  process.

Thank you again for contributing!  We will try to test and integrate the change
as soon as we can, but be aware we have many GitHub repositories to manage and
can't immediately respond to every request.  There is no need to bump or check in
on a pull request (it will clutter the discussion of the request).

Also don't be worried if the request is closed or not integrated--sometimes the
priorities of Adafruit's GitHub code (education, ease of use) might not match the
priorities of the pull request.  Don't fret, the open source community thrives on
forks and GitHub makes it easy to keep your changes in a forked repo.

After reviewing the guidelines above you can delete this text from the pull request.
