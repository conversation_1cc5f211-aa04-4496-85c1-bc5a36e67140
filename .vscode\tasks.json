{"version": "2.0.0", "tasks": [{"type": "PlatformIO", "task": "Build", "group": {"kind": "build", "isDefault": true}, "problemMatcher": ["$platformio"], "label": "PlatformIO: Build"}, {"type": "PlatformIO", "task": "Upload", "group": "build", "problemMatcher": ["$platformio"], "label": "PlatformIO: Upload"}, {"type": "PlatformIO", "task": "Clean", "group": "build", "problemMatcher": ["$platformio"], "label": "PlatformIO: Clean"}, {"type": "PlatformIO", "task": "Monitor", "group": "build", "problemMatcher": ["$platformio"], "label": "PlatformIO: Monitor"}]}