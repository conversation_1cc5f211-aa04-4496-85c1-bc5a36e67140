# ESP32 Soil Moisture Monitoring System

A comprehensive Arduino program for monitoring soil moisture, temperature, and humidity using an ESP32 microcontroller with automatic irrigation control.

## 🔧 Hardware Components

Based on the pin configuration in `pinout.md`:

- **NodeMCU ESP32 Development Board**
- **Soil Moisture Sensor** (Analog) → GPIO 2
- **DHT22 Temperature/Humidity Sensor** → GPIO 15
- **Relay/LED Control** → GPIO 0 (boot-safe)

## 📚 Required Libraries

Install these libraries through the Arduino IDE Library Manager:

1. **DHT sensor library** by Adafruit
   - Go to: Sketch → Include Library → Manage Libraries
   - Search for "DHT sensor library"
   - Install "DHT sensor library" by Adafruit

2. **Adafruit Unified Sensor** (dependency for DHT)
   - This should install automatically with the DHT library
   - If not, search for "Adafruit Unified Sensor" and install

## 🚀 Features

- **Multi-sensor monitoring**: Soil moisture, temperature, and humidity
- **Imperial/Metric units**: Temperature displayed in °F (primary) and °C (secondary)
- **Automatic irrigation**: Relay activates when soil moisture drops below threshold
- **Error handling**: Detects and reports sensor failures
- **Formatted output**: Clean tabular display of all readings
- **System health monitoring**: Alerts for multiple consecutive sensor errors
- **Modular code structure**: Easy to expand and modify

## 📊 Sample Output

```
╔═══════════════════════════════════════════════════════════╗
║           ESP32 Soil Moisture Monitoring System          ║
║                                                           ║
║  Components:                                              ║
║  • Soil Moisture Sensor (GPIO 2)                         ║
║  • DHT22 Temp/Humidity (GPIO 15)                         ║
║  • Relay/LED Control (GPIO 0)                            ║
║                                                           ║
║  Units: Imperial (Primary) / Metric (Secondary)          ║
╚═══════════════════════════════════════════════════════════╝

┌──────┬─────────────────┬─────────────┬─────────────────┬────────┐
│ #    │ Temperature     │ Humidity    │ Soil Moisture   │ Relay  │
│      │ °F (°C)         │ %           │ % (Raw)         │ Status │
├──────┼─────────────────┼─────────────┼─────────────────┼────────┤
│    1 │  75.2°F (24.0°C)│     65.3%   │  45.2% (2850)   │ OFF    │
│    2 │  75.4°F (24.1°C)│     65.1%   │  43.8% (2920)   │ OFF    │
│    3 │  75.3°F (24.0°C)│     64.9%   │  35.2% (3150)   │  ON    │
├──────┴─────────────────┴─────────────┴─────────────────┴────────┤
│ IRRIGATION ACTIVATED - Moisture: 35.2%                          │
├──────┬─────────────────┬─────────────┬─────────────────┬────────┤
```

## ⚙️ Configuration

### Soil Moisture Sensor Calibration

The program includes default calibration values that you should adjust for your specific sensor:

```cpp
#define MOISTURE_DRY 1500   // Raw value when sensor is in dry soil
#define MOISTURE_WET 1000   // Raw value when sensor is in wet soil
#define MOISTURE_THRESHOLD 40.0 // Moisture percentage threshold for irrigation
```

**To calibrate:**

1. **Dry Calibration**: Place sensor in completely dry soil, note the raw value
2. **Wet Calibration**: Place sensor in saturated soil, note the raw value  
3. **Update the defines** in the code with your measured values
4. **Set threshold**: Adjust `MOISTURE_THRESHOLD` for when irrigation should activate

### Other Configurable Parameters

```cpp
#define MOISTURE_SAMPLES 5      // Number of samples for averaging (accuracy)
#define READING_INTERVAL 2500   // Time between readings in milliseconds
```

## 🔌 Wiring Diagram

```
NodeMCU ESP32 Pin    →    Component
─────────────────────────────────────
GPIO 2       →    Soil Moisture Sensor (Analog Out)
GPIO 15      →    DHT22 Data Pin
GPIO 0       →    Relay/LED Control (boot-safe)
3.3V         →    DHT22 VCC, Soil Sensor VCC
GND          →    All component grounds
```

## 📋 Setup Instructions

1. **Install Arduino IDE** (if not already installed)
2. **Add ESP32 board support**:
   - File → Preferences → Additional Board Manager URLs
   - Add: `https://dl.espressif.com/dl/package_esp32_index.json`
   - Tools → Board → Boards Manager → Search "ESP32" → Install
3. **Install required libraries** (see above)
4. **Connect hardware** according to pinout.md
5. **Upload the code** to your ESP32
6. **Open Serial Monitor** (115200 baud) to view readings
7. **Calibrate sensors** as needed

## 🛠️ Troubleshooting

### Common Issues

**DHT22 not responding:**
- Check wiring connections
- Ensure 3.3V power supply
- Try a different GPIO pin
- Add a 10kΩ pull-up resistor between data pin and VCC

**Soil moisture readings seem wrong:**
- Calibrate the sensor with dry and wet soil
- Check analog connection to GPIO 2
- Verify sensor is getting power

**Relay not activating:**
- Check GPIO 0 connection
- Verify relay power requirements
- Test with an LED first
- Note: GPIO 0 is boot-safe (prevents clicking during startup)

### Serial Monitor Shows Errors

- **"DHT22 sensor not responding"**: Check DHT22 wiring and power
- **"Soil moisture sensor readings unusual"**: Check analog sensor connection
- **"Multiple sensor errors detected"**: Check all connections and power supply

## 🔄 Future Expansion Ideas

The modular code structure makes it easy to add:

- **WiFi connectivity** for remote monitoring
- **Data logging** to SD card or cloud
- **Additional sensors** (pH, light, etc.)
- **Web interface** for configuration
- **Mobile app integration**
- **Multiple soil sensors** for different zones

## 📝 Code Structure

- **Setup Functions**: Hardware initialization and component testing
- **Sensor Reading Functions**: Modular sensor data collection
- **Display Functions**: Formatted output and user interface
- **Control Functions**: Irrigation logic and system health monitoring
- **Utility Functions**: Calibration helpers and diagnostics

## 📄 License

This project is open source and available for educational and personal use.
