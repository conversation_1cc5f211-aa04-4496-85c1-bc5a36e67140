{"configurations": [{"name": "PlatformIO", "includePath": ["${workspaceFolder}/src", "${workspaceFolder}/include", "${workspaceFolder}/.pio/build/nodemcu-32s", "${workspaceFolder}/.pio/libdeps/nodemcu-32s/DHT sensor library", "${workspaceFolder}/.pio/libdeps/nodemcu-32s/Adafruit Unified Sensor", "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include", "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/**", "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32", "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/variants/nodemcu-32s", "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/**", "C:/Users/<USER>/.platformio/packages/toolchain-xtensa-esp32/xtensa-esp32-elf/include/**", "C:/Users/<USER>/.platformio/packages/toolchain-xtensa-esp32/lib/gcc/xtensa-esp32-elf/8.4.0/include/**"], "browse": {"limitSymbolsToIncludedHeaders": true, "path": ["${workspaceFolder}/src", "${workspaceFolder}/include", "${workspaceFolder}/.pio/build/nodemcu-32s", "${workspaceFolder}/.pio/libdeps/nodemcu-32s", "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include", "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32", "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/variants/nodemcu-32s", "C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries", "C:/Users/<USER>/.platformio/packages/toolchain-xtensa-esp32/xtensa-esp32-elf/include"]}, "defines": ["PLATFORMIO=60118", "ARDUINO_NodeMCU_32S", "CORE_DEBUG_LEVEL=3", "BOARD_HAS_PSRAM", "ARDUINO_USB_CDC_ON_BOOT=0", "HAVE_CONFIG_H", "MBEDTLS_CONFIG_FILE=\"mbedtls/esp_config.h\"", "UNITY_INCLUDE_CONFIG_H", "WITH_POSIX", "_GNU_SOURCE", "IDF_VER=\"v4.4.7-dirty\"", "ESP_PLATFORM", "_POSIX_READER_WRITER_LOCKS", "ARDUINO_ARCH_ESP32", "ESP32", "F_CPU=240000000L", "ARDUINO=10812", "ARDUINO_VARIANT=\"nodemcu-32s\"", "ARDUINO_BOARD=\"NodeMCU-32S\"", "ARDUINO_PARTITION_huge_app"], "compilerPath": "C:/Users/<USER>/.platformio/packages/toolchain-xtensa-esp32/bin/xtensa-esp32-elf-gcc.exe", "cStandard": "c11", "cppStandard": "c++11", "intelliSenseMode": "gcc-x64", "forcedInclude": ["C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_common/include/esp_err.h"]}], "version": 4}