{"version": "0.2.0", "configurations": [{"type": "platformio-debug", "request": "launch", "name": "PIO Debug", "executable": "${workspaceFolder}/.pio/build/nodemcu-32s/firmware.elf", "projectEnvName": "nodemcu-32s", "toolchainBinDir": "C:/Users/<USER>/.platformio/packages/toolchain-xtensa-esp32/bin", "internalConsoleOptions": "openOnSessionStart", "preLaunchTask": {"type": "PlatformIO", "task": "Pre-Debug"}}, {"type": "platformio-debug", "request": "launch", "name": "<PERSON><PERSON>bug (skip Pre-Debug)", "executable": "${workspaceFolder}/.pio/build/nodemcu-32s/firmware.elf", "projectEnvName": "nodemcu-32s", "toolchainBinDir": "C:/Users/<USER>/.platformio/packages/toolchain-xtensa-esp32/bin", "internalConsoleOptions": "openOnSessionStart"}]}